#!/usr/bin/env python3
"""
Test script to verify that warnings are properly suppressed.
"""

import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Import and setup warning suppression early
from src.photo_center.utils.warnings_suppressor import setup_quiet_environment
setup_quiet_environment()

def test_model_loading():
    """Test loading models to see if warnings are suppressed."""
    print("Testing model loading with warning suppression...")
    
    try:
        from src.photo_center.utils.config import Config
        from src.photo_center.models.unified_detector import UnifiedHumanDetector
        
        print("✓ Imports successful")
        
        # Create config
        config = Config()
        print("✓ Config loaded")
        
        # Initialize detector (this should trigger model loading)
        print("Initializing unified detector...")
        detector = UnifiedHumanDetector(config)
        print("✓ Unified detector initialized")
        
        print(f"✓ Current model type: {detector.get_current_model_type()}")
        
        print("\n✅ Test completed successfully!")
        print("If you see this message without TensorFlow/MediaPipe warnings above,")
        print("then the warning suppression is working correctly.")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        return False

if __name__ == "__main__":
    success = test_model_loading()
    sys.exit(0 if success else 1)
