#!/usr/bin/env python3
"""
Quiet launcher for Photo Center that suppresses ML framework warnings.

This script sets environment variables before importing any ML libraries
to suppress TensorFlow and MediaPipe warnings at the C++ level.
"""

import os
import sys
import subprocess
from pathlib import Path

def setup_environment():
    """Set environment variables to suppress ML framework warnings."""
    # TensorFlow warnings suppression
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'  # Suppress all TF logs except errors
    os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'  # Disable oneDNN optimizations
    
    # Google logging (used by MediaPipe) suppression
    os.environ['GLOG_minloglevel'] = '3'  # Only show fatal errors
    os.environ['GLOG_stderrthreshold'] = '3'  # Only show fatal errors on stderr
    os.environ['GLOG_v'] = '0'  # Disable verbose logging
    
    # MediaPipe specific
    os.environ['MEDIAPIPE_DISABLE_GPU'] = '0'  # Keep GPU enabled but reduce logs
    
    # Python warnings
    os.environ['PYTHONWARNINGS'] = 'ignore::UserWarning:mediapipe,ignore::FutureWarning:tensorflow'

def main():
    """Main entry point that launches Photo Center with suppressed warnings."""
    setup_environment()
    
    # Add src to path for imports
    src_path = str(Path(__file__).parent / "src")
    sys.path.insert(0, src_path)
    
    # Import and run the main application
    try:
        from src.photo_center.main import main as photo_center_main
        return photo_center_main()
    except ImportError:
        # Fallback to running the main.py script
        main_script = Path(__file__).parent / "main.py"
        result = subprocess.run([sys.executable, str(main_script)] + sys.argv[1:])
        return result.returncode

if __name__ == "__main__":
    sys.exit(main())
