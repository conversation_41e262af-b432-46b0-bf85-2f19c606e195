#!/usr/bin/env python3
"""
Run Photo Center with YOLO-only detection to avoid MediaPipe warnings.

This script temporarily modifies the configuration to use only YOLO detection,
which eliminates MediaPipe warnings entirely.
"""

import sys
import yaml
from pathlib import Path

def modify_config_for_yolo_only():
    """Temporarily modify config to use YOLO only."""
    config_path = Path("config.yaml")
    
    if not config_path.exists():
        print("Error: config.yaml not found")
        return False
    
    # Read current config
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    # Backup original setting
    original_model = config.get('models', {}).get('detection_model', 'auto')
    
    # Set to YOLO only
    if 'models' not in config:
        config['models'] = {}
    config['models']['detection_model'] = 'yolo'
    
    # Write modified config
    with open(config_path, 'w') as f:
        yaml.dump(config, f, default_flow_style=False, indent=2)
    
    print(f"✓ Temporarily set detection model to 'yolo' (was '{original_model}')")
    return True, original_model

def restore_config(original_model):
    """Restore original config setting."""
    config_path = Path("config.yaml")
    
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    config['models']['detection_model'] = original_model
    
    with open(config_path, 'w') as f:
        yaml.dump(config, f, default_flow_style=False, indent=2)
    
    print(f"✓ Restored detection model to '{original_model}'")

def main():
    """Main entry point."""
    print("Photo Center - YOLO-Only Mode (No MediaPipe Warnings)")
    print("=" * 50)
    
    # Modify config
    result = modify_config_for_yolo_only()
    if not result:
        return 1
    
    success, original_model = result
    
    try:
        # Add src to path for imports
        sys.path.insert(0, str(Path(__file__).parent / "src"))
        
        # Import and run the main application
        from src.photo_center.main import main as photo_center_main
        return_code = photo_center_main()
        
    except Exception as e:
        print(f"Error running Photo Center: {e}")
        return_code = 1
    
    finally:
        # Restore original config
        restore_config(original_model)
    
    return return_code

if __name__ == "__main__":
    sys.exit(main())
