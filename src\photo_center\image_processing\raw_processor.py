"""RAW image processing using rawpy."""

import rawpy
import imageio
import numpy as np
from pathlib import Path
from typing import Op<PERSON>, Tuple, Union
from PIL import Image
import cv2

from ..utils.logger import get_logger
from ..utils.config import Config


class RawProcessor:
    """RAW image processor using rawpy and imageio."""
    
    def __init__(self, config: Optional[Config] = None):
        """Initialize RAW processor.
        
        Args:
            config: Configuration object. If None, creates default config.
        """
        self.config = config or Config()
        self.logger = get_logger(__name__)
        
        # Supported RAW extensions
        self.raw_extensions = {'.cr2', '.nef', '.arw', '.dng', '.raw', '.orf', '.rw2', '.pef', '.srw'}
        
    def is_raw_file(self, file_path: Union[str, Path]) -> bool:
        """Check if file is a RAW image file.
        
        Args:
            file_path: Path to image file
            
        Returns:
            True if file is a RAW image
        """
        return Path(file_path).suffix.lower() in self.raw_extensions
    
    def load_image(self, file_path: Union[str, Path]) -> np.ndarray:
        """Load image from file (RAW or standard format).
        
        Args:
            file_path: Path to image file
            
        Returns:
            Image as numpy array in BGR format (for OpenCV compatibility)
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"Image file not found: {file_path}")
        
        try:
            if self.is_raw_file(file_path):
                return self._load_raw_image(file_path)
            else:
                return self._load_standard_image(file_path)
                
        except Exception as e:
            self.logger.error(f"Failed to load image {file_path}: {e}")
            raise
    
    def _load_raw_image(self, file_path: Path) -> np.ndarray:
        """Load RAW image using rawpy.
        
        Args:
            file_path: Path to RAW image file
            
        Returns:
            Processed image as numpy array in BGR format
        """
        self.logger.info(f"Loading RAW image: {file_path}")
        
        with rawpy.imread(str(file_path)) as raw:
            # Post-process RAW image
            # Use default settings for now, can be customized later
            rgb_image = raw.postprocess(
                use_camera_wb=True,  # Use camera white balance
                half_size=False,     # Full resolution
                no_auto_bright=True, # Disable auto brightness
                output_bps=16        # 16-bit output
            )
        
        # Convert from RGB to BGR for OpenCV
        bgr_image = cv2.cvtColor(rgb_image, cv2.COLOR_RGB2BGR)
        
        # Convert from 16-bit to 8-bit if needed
        if bgr_image.dtype == np.uint16:
            bgr_image = (bgr_image / 256).astype(np.uint8)
        
        self.logger.debug(f"RAW image loaded: shape={bgr_image.shape}, dtype={bgr_image.dtype}")
        return bgr_image
    
    def _load_standard_image(self, file_path: Path) -> np.ndarray:
        """Load standard image format using OpenCV.
        
        Args:
            file_path: Path to image file
            
        Returns:
            Image as numpy array in BGR format
        """
        self.logger.debug(f"Loading standard image: {file_path}")
        
        image = cv2.imread(str(file_path))
        if image is None:
            raise ValueError(f"Could not load image: {file_path}")
        
        return image
    
    def get_output_format_from_input(self, input_path: Union[str, Path]) -> str:
        """Determine appropriate output format based on input file.

        Args:
            input_path: Path to input image file

        Returns:
            Appropriate output format string (jpg, png, tiff, or original extension)
        """
        input_path = Path(input_path)
        input_ext = input_path.suffix.lower()

        # For RAW files, return the original extension to preserve it in filename
        # The actual format will be TIFF but we keep the original extension
        if self.is_raw_file(input_path):
            return input_ext[1:]  # Remove the dot

        # Map common extensions to our supported formats
        format_mapping = {
            '.jpg': 'jpg',
            '.jpeg': 'jpg',
            '.png': 'png',
            '.tiff': 'tiff',
            '.tif': 'tiff'
        }

        return format_mapping.get(input_ext, self.config.output_format)

    def get_actual_save_format(self, format_string: str) -> str:
        """Get the actual format to use for saving (maps RAW extensions to TIFF).

        Args:
            format_string: Format string (could be RAW extension)

        Returns:
            Actual format for saving (jpg, png, tiff)
        """
        # If it's a RAW extension, save as TIFF for best quality
        if format_string.lower() in ['cr2', 'nef', 'arw', 'dng', 'raw', 'orf', 'rw2', 'pef', 'srw']:
            return 'tiff'

        return format_string

    def save_image(
        self,
        image: np.ndarray,
        output_path: Union[str, Path],
        quality: Optional[int] = None,
        format_override: Optional[str] = None,
        original_path: Optional[Union[str, Path]] = None
    ) -> None:
        """Save image to file.

        Args:
            image: Image as numpy array in BGR format
            output_path: Output file path
            quality: JPEG quality (1-100). If None, uses config default
            format_override: Override output format. If None, tries to preserve original format
            original_path: Path to original file to preserve format from
        """
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # Determine output format with priority:
        # 1. Explicit format override
        # 2. Format from original file (if provided)
        # 3. Config default
        if format_override:
            output_format = format_override
        elif original_path:
            output_format = self.get_output_format_from_input(original_path)
        else:
            output_format = self.config.output_format

        # Get the actual format to save as (maps RAW extensions to TIFF)
        actual_format = self.get_actual_save_format(output_format)
        quality = quality or self.config.output_quality

        # Determine file extension to use
        if output_format != actual_format:
            # This is a RAW file - keep original extension but save as TIFF
            final_path = output_path.with_suffix(f'.{output_format}')
            is_raw_conversion = True
        else:
            is_raw_conversion = False

        try:
            if actual_format.lower() in ['jpg', 'jpeg']:
                # Save as JPEG
                if not is_raw_conversion:
                    final_path = output_path.with_suffix('.jpg')
                cv2.imwrite(
                    str(final_path),
                    image,
                    [cv2.IMWRITE_JPEG_QUALITY, quality]
                )
            elif actual_format.lower() == 'png':
                # Save as PNG
                if not is_raw_conversion:
                    final_path = output_path.with_suffix('.png')
                cv2.imwrite(str(final_path), image)
            elif actual_format.lower() in ['tiff', 'tif']:
                # Save as TIFF
                if not is_raw_conversion:
                    final_path = output_path.with_suffix('.tiff')
                cv2.imwrite(str(final_path), image)
            else:
                # Default to JPEG
                if not is_raw_conversion:
                    final_path = output_path.with_suffix('.jpg')
                cv2.imwrite(
                    str(final_path),
                    image,
                    [cv2.IMWRITE_JPEG_QUALITY, quality]
                )

            if is_raw_conversion:
                self.logger.info(f"RAW image processed and saved as TIFF with original extension: {final_path}")
            else:
                self.logger.info(f"Image saved: {final_path}")

        except Exception as e:
            self.logger.error(f"Failed to save image {output_path}: {e}")
            raise
    
    def resize_image(
        self, 
        image: np.ndarray, 
        max_size: Optional[Tuple[int, int]] = None,
        maintain_aspect: bool = True
    ) -> np.ndarray:
        """Resize image while maintaining aspect ratio.
        
        Args:
            image: Input image
            max_size: Maximum size as (width, height). If None, uses config default
            maintain_aspect: Whether to maintain aspect ratio
            
        Returns:
            Resized image
        """
        if max_size is None:
            max_size = self.config.get('image_processing.max_output_size')
            if max_size is None:
                return image  # No resizing
        
        height, width = image.shape[:2]
        max_width, max_height = max_size
        
        if width <= max_width and height <= max_height:
            return image  # No resizing needed
        
        if maintain_aspect:
            # Calculate scaling factor
            scale = min(max_width / width, max_height / height)
            new_width = int(width * scale)
            new_height = int(height * scale)
        else:
            new_width, new_height = max_width, max_height
        
        resized = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
        self.logger.debug(f"Image resized from {width}x{height} to {new_width}x{new_height}")
        
        return resized
    
    def get_image_info(self, file_path: Union[str, Path]) -> dict:
        """Get image information without loading the full image.
        
        Args:
            file_path: Path to image file
            
        Returns:
            Dictionary with image information
        """
        file_path = Path(file_path)
        
        info = {
            'path': str(file_path),
            'exists': file_path.exists(),
            'size_bytes': file_path.stat().st_size if file_path.exists() else 0,
            'is_raw': self.is_raw_file(file_path),
            'extension': file_path.suffix.lower()
        }
        
        try:
            if self.is_raw_file(file_path):
                # Get RAW image info
                with rawpy.imread(str(file_path)) as raw:
                    info.update({
                        'width': raw.sizes.width,
                        'height': raw.sizes.height,
                        'camera_make': getattr(raw, 'camera_make', 'Unknown'),
                        'camera_model': getattr(raw, 'camera_model', 'Unknown'),
                    })
            else:
                # Get standard image info using PIL
                with Image.open(file_path) as img:
                    info.update({
                        'width': img.width,
                        'height': img.height,
                        'mode': img.mode,
                        'format': img.format
                    })
        
        except Exception as e:
            self.logger.warning(f"Could not get image info for {file_path}: {e}")
            info['error'] = str(e)
        
        return info
