@echo off
REM Quiet launcher for Photo Center that suppresses ML framework warnings

REM Set environment variables to suppress warnings
set TF_CPP_MIN_LOG_LEVEL=3
set TF_ENABLE_ONEDNN_OPTS=0
set GLOG_minloglevel=3
set GLOG_stderrthreshold=3
set GLOG_v=0
set MEDIAPIPE_DISABLE_GPU=0
set PYTHONWARNINGS=ignore::UserWarning:mediapipe,ignore::FutureWarning:tensorflow

REM Run the application
uv run .\main.py %*
