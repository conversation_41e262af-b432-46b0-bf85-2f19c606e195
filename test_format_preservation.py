#!/usr/bin/env python3
"""Test script to verify format preservation functionality."""

import tempfile
import numpy as np
from pathlib import Path
import sys
import os

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from photo_center.image_processing.raw_processor import RawProcessor
from photo_center.utils.config import Config

def create_test_image():
    """Create a simple test image."""
    # Create a 100x100 RGB image
    image = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
    return image

def test_format_preservation():
    """Test that file formats are preserved correctly."""
    print("Testing format preservation...")
    
    config = Config()
    processor = RawProcessor(config)
    test_image = create_test_image()
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Test cases: (original_ext, expected_format, expected_actual_format)
        test_cases = [
            ('.jpg', 'jpg', 'jpg'),
            ('.png', 'png', 'png'),
            ('.tiff', 'tiff', 'tiff'),
            ('.cr2', 'cr2', 'tiff'),  # RAW file should save as TIFF but keep .cr2 extension
            ('.nef', 'nef', 'tiff'),  # RAW file should save as TIFF but keep .nef extension
        ]
        
        for original_ext, expected_format, expected_actual_format in test_cases:
            print(f"\nTesting {original_ext}...")
            
            # Create fake original file path
            original_file = temp_path / f"original{original_ext}"
            
            # Test get_output_format_from_input
            detected_format = processor.get_output_format_from_input(original_file)
            print(f"  Detected format: {detected_format}")
            assert detected_format == expected_format, f"Expected {expected_format}, got {detected_format}"
            
            # Test get_actual_save_format
            actual_format = processor.get_actual_save_format(detected_format)
            print(f"  Actual save format: {actual_format}")
            assert actual_format == expected_actual_format, f"Expected {expected_actual_format}, got {actual_format}"
            
            # Test saving with original path
            output_file = temp_path / f"output{original_ext}"
            try:
                processor.save_image(
                    test_image,
                    output_file,
                    original_path=original_file
                )
                
                # Check that file was created with correct extension
                assert output_file.exists(), f"Output file {output_file} was not created"
                print(f"  ✓ File saved successfully: {output_file}")
                
            except Exception as e:
                print(f"  ✗ Error saving {original_ext}: {e}")
                raise
    
    print("\n✓ All format preservation tests passed!")

def test_default_behavior():
    """Test default behavior without original path."""
    print("\nTesting default behavior...")
    
    config = Config()
    processor = RawProcessor(config)
    test_image = create_test_image()
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        output_file = temp_path / "test_default"
        
        # Should use config default (jpg)
        processor.save_image(test_image, output_file)
        
        expected_file = output_file.with_suffix('.jpg')
        assert expected_file.exists(), f"Default output file {expected_file} was not created"
        print(f"  ✓ Default format saved successfully: {expected_file}")
    
    print("✓ Default behavior test passed!")

if __name__ == "__main__":
    try:
        test_format_preservation()
        test_default_behavior()
        print("\n🎉 All tests passed! Format preservation is working correctly.")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        sys.exit(1)
