# Photo Center - Warning Messages Guide

## Overview

When running Photo Center, you may see some warning messages from TensorFlow Lite and MediaPipe. These warnings are **harmless** and do not affect the functionality of the application. This guide explains what they mean and how to minimize them.

## Common Warning Messages

### 1. TensorFlow Lite XNNPACK Delegate
```
INFO: Created TensorFlow Lite XNNPACK delegate for CPU.
```
**What it means:** TensorFlow Lite is using CPU optimization (XNNPACK) for better performance.
**Impact:** None - this is actually good for performance.
**Action:** No action needed.

### 2. Feedback Manager Warnings
```
W0000 00:00:1749509264.835873   53828 inference_feedback_manager.cc:114] Feedback manager requires a model with a single signature inference. Disabling support for feedback tensors.
```
**What it means:** MediaPipe's feedback system is disabled because the pose model doesn't support it.
**Impact:** None - feedback tensors are not needed for pose detection.
**Action:** No action needed.

### 3. Landmark Projection Calculator Warning
```
W0000 00:00:1749509264.962349   34228 landmark_projection_calculator.cc:186] Using NORM_RECT without IMAGE_DIMENSIONS is only supported for the square ROI. Provide IMAGE_DIMENSIONS or use PROJECTION_MATRIX.
```
**What it means:** MediaPipe is using normalized rectangles without explicit image dimensions.
**Impact:** None - the pose detection still works correctly.
**Action:** No action needed.

## Why These Warnings Appear

These warnings come from the C++ level of MediaPipe and TensorFlow Lite libraries. They are printed before Python code can suppress them, making them difficult to completely eliminate.

## Minimizing Warnings

Photo Center includes several mechanisms to reduce warnings:

### 1. Environment Variables (Automatic)
The application automatically sets these environment variables:
- `TF_CPP_MIN_LOG_LEVEL=2` - Reduces TensorFlow logging
- `GLOG_minloglevel=2` - Reduces Google logging (MediaPipe)
- `TF_ENABLE_ONEDNN_OPTS=0` - Disables oneDNN optimization warnings

### 2. Configuration Options
In `config.yaml`, you can adjust:
```yaml
models:
  openpose:
    model_complexity: 1  # Use lower complexity (0=lite, 1=full, 2=heavy)
    suppress_warnings: true  # Enable warning suppression
```

### 3. Using the Quiet Launcher
Use the provided batch script for minimal warnings:
```bash
# Windows
.\run_quiet.bat

# Or set environment variables manually
set TF_CPP_MIN_LOG_LEVEL=3
set GLOG_minloglevel=3
uv run .\main.py
```

## Alternative: Use YOLO Only
If the warnings bother you, you can disable MediaPipe entirely:

1. Edit `config.yaml`:
```yaml
models:
  detection_model: "yolo"  # Use only YOLO, no MediaPipe
```

2. This eliminates MediaPipe warnings but you lose pose keypoint detection.

## Summary

- **These warnings are harmless** and don't affect functionality
- They appear briefly during startup and then stop
- The application works perfectly despite these messages
- Complete suppression is technically challenging due to C++ library behavior
- Use the quiet launcher or YOLO-only mode if warnings are problematic

## Technical Details

The warnings originate from:
1. **TensorFlow Lite**: Low-level tensor operations and delegate initialization
2. **MediaPipe**: Pose model initialization and graph configuration
3. **ABSL Logging**: Google's logging library used by MediaPipe

These are logged at the C++ level before Python logging configuration takes effect, making them nearly impossible to suppress completely without modifying the source libraries.
